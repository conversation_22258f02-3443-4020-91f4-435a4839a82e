# 追加服务模块接口文档

## 概述
追加服务模块允许用户在服务进行中申请额外的服务项目，员工可以确认或拒绝这些申请，用户支付后即可享受追加服务。

## 重要更新说明

**⚠️ 接口返回结构变更（2025-07-01）**

`GET /order-details/{orderDetailId}/additional-services` 接口的返回结构已发生重大变更：

**变更原因：** 修复了重要的业务逻辑漏洞。增项服务存在于两个位置：
1. **主订单时选择的增项服务**：存储在 `order_detail_additional` 关联表中
2. **后续追加的服务订单**：存储在 `additional_service_orders` 表中

**变更前：** 只返回后续追加的服务订单数组
**变更后：** 返回包含两种增项服务的对象结构

**影响范围：**
- 用户端查询增项服务列表接口
- 员工端查询增项服务列表接口（复用同一接口）

**前端适配建议：**
```javascript
// 旧版本处理方式
const additionalServices = response.data; // 直接是数组

// 新版本处理方式
const { originalAdditionalServices, additionalServiceOrders, summary } = response.data;
// 需要分别处理两种类型的增项服务
```

## 接口分类
本文档按照使用端进行分组，部分接口在多个端使用时会在相应章节中重复说明。

## 统一返回格式说明

### 成功响应格式
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    // 具体业务数据
  }
}
```

### 错误响应格式
```json
{
  "errCode": 400, // 错误码，自定义错误为status值，系统错误为500
  "msg": "错误信息"
}
```

## 追加服务订单状态枚举
```typescript
enum AdditionalServiceOrderStatus {
  PENDING_CONFIRM = 'pending_confirm',    // 待确认
  CONFIRMED = 'confirmed',                // 已确认
  REJECTED = 'rejected',                  // 已拒绝
  PENDING_PAYMENT = 'pending_payment',    // 待付款
  PAID = 'paid',                         // 已付款/服务中
  COMPLETED = 'completed',               // 已完成
  CANCELLED = 'cancelled',               // 已取消
  REFUNDING = 'refunding',               // 退款中
  REFUNDED = 'refunded'                  // 已退款
}
```

---

## 1. 用户端接口

### 1.1 创建追加服务申请
**接口地址：** `POST /order-details/{orderDetailId}/additional-services`  
**接口描述：** 用户为服务中的订单申请追加服务  
**是否需要认证：** 是

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| orderDetailId | number | 是 | 订单详情ID |

**请求参数：**
```json
{
  "customerId": 123,
  "services": [
    {
      "serviceId": 1,
      "quantity": 2
    }
  ],
  "discountInfos": [
    {
      "discountType": "coupon",
      "discountId": 1,
      "discountAmount": 10.00
    }
  ],
  "remark": "需要额外清洁"
}
```

**参数说明：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| customerId | number | 是 | 客户ID |
| services | array | 是 | 追加服务列表 |
| services[].serviceId | number | 是 | 服务ID |
| services[].quantity | number | 是 | 服务数量 |
| discountInfos | array | 否 | 优惠信息列表 |
| discountInfos[].discountType | string | 否 | 优惠类型：coupon-代金券，membership_card-权益卡 |
| discountInfos[].discountId | number | 否 | 优惠ID |
| discountInfos[].discountAmount | number | 否 | 优惠金额 |
| remark | string | 否 | 备注 |

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "id": 1,
    "sn": "ADD1703123456781234",
    "orderDetailId": 123,
    "customerId": 456,
    "status": "pending_confirm",
    "originalPrice": 100.00,
    "totalFee": 90.00,
    "cardDeduction": 0.00,
    "couponDeduction": 10.00,
    "createdAt": "2023-12-21T10:30:00.000Z"
  }
}
```

### 1.2 查询追加服务列表
**接口地址：** `GET /order-details/{orderDetailId}/additional-services`
**接口描述：** 查询指定订单详情的所有增项服务，包括主订单时选择的增项服务和后续追加的服务订单
**是否需要认证：** 是

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| orderDetailId | number | 是 | 订单详情ID |

**查询参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| status | string | 否 | 状态筛选（仅对追加服务订单有效） |

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "originalAdditionalServices": [
      {
        "id": 2,
        "name": "全身去油",
        "price": "150.00",
        "type": "JU_BU",
        "description": "深度清洁毛发油脂"
      },
      {
        "id": 3,
        "name": "除菌消毒",
        "price": "80.00",
        "type": "QUAN_SHEN",
        "description": "全身除菌消毒处理"
      }
    ],
    "additionalServiceOrders": [
      {
        "id": 1,
        "sn": "ADD1703123456781234",
        "orderDetailId": 123,
        "status": "pending_confirm",
        "originalPrice": 100.00,
        "totalFee": 90.00,
        "details": [
          {
            "id": 1,
            "serviceId": 1,
            "serviceName": "深度清洁",
            "servicePrice": 50.00,
            "quantity": 2,
            "service": {
              "id": 1,
              "serviceName": "深度清洁",
              "basePrice": "50.00",
              "avgDuration": 30
            }
          }
        ],
        "customer": {
          "id": 456,
          "nickname": "张三",
          "phone": "13800138000"
        },
        "employee": {
          "id": 789,
          "name": "李师傅",
          "phone": "13900139000"
        },
        "createdAt": "2023-12-21T10:30:00.000Z"
      }
    ],
    "summary": {
      "originalCount": 2,
      "additionalOrdersCount": 1,
      "totalCount": 3
    }
  }
}
```

**返回数据说明：**
- `originalAdditionalServices`: 主订单下单时选择的增项服务列表
  - 这些服务已包含在主订单价格中，无需额外支付
  - 来源于 `order_detail_additional` 关联表
- `additionalServiceOrders`: 后续追加的服务订单列表
  - 这些是服务过程中临时申请的追加服务
  - 需要经过确认、支付等流程
  - 来源于 `additional_service_orders` 表
- `summary`: 统计信息
  - `originalCount`: 主订单增项服务数量
  - `additionalOrdersCount`: 追加服务订单数量
  - `totalCount`: 总增项服务数量

### 1.3 查询追加服务详情
**接口地址：** `GET /order-details/{orderDetailId}/additional-services/{id}`  
**接口描述：** 查询指定追加服务的详细信息  
**是否需要认证：** 是

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| orderDetailId | number | 是 | 订单详情ID |
| id | number | 是 | 追加服务订单ID |

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "id": 1,
    "sn": "ADD1703123456781234",
    "orderDetailId": 123,
    "status": "confirmed",
    "originalPrice": 100.00,
    "totalFee": 90.00,
    "cardDeduction": 0.00,
    "couponDeduction": 10.00,
    "confirmTime": "2023-12-21T11:00:00.000Z",
    "details": [
      {
        "id": 1,
        "serviceId": 1,
        "serviceName": "深度清洁",
        "servicePrice": 50.00,
        "quantity": 2
      }
    ],
    "discountInfos": [
      {
        "id": 1,
        "discountType": "coupon",
        "discountId": 1,
        "discountAmount": 10.00
      }
    ],
    "orderDetail": {
      "id": 123,
      "order": {
        "id": 100,
        "sn": "1703123456781234",
        "customer": {
          "id": 456,
          "name": "张三",
          "phone": "13800138000"
        }
      }
    }
  }
}
```

### 1.4 支付追加服务订单
**接口地址：** `POST /order-details/{orderDetailId}/additional-services/{id}/pay`
**接口描述：** 用户支付已确认的追加服务订单
**是否需要认证：** 是

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| orderDetailId | number | 是 | 订单详情ID |
| id | number | 是 | 追加服务订单ID |

**请求参数：**
```json
{
  "customerId": 456
}
```

**响应示例：**

**0元订单（直接支付成功）：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "success": true,
    "message": "0元订单支付成功"
  }
}
```

**需要微信支付的订单：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "success": false,
    "needPay": true,
    "orderSn": "ADD1703123456781234",
    "totalFee": 90.00,
    "message": "需要调用微信支付"
  }
}
```

**说明：**
- 如果是0元订单，直接支付成功，订单状态变为已支付
- 如果需要微信支付，前端需要使用返回的`orderSn`调用微信支付接口
- 微信支付成功后，系统会通过支付回调自动更新订单状态

### 1.5 删除追加服务申请
**接口地址：** `DELETE /order-details/{orderDetailId}/additional-services/{id}`
**接口描述：** 用户删除待确认状态的追加服务申请
**是否需要认证：** 是

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| orderDetailId | number | 是 | 订单详情ID |
| id | number | 是 | 追加服务订单ID |

**请求参数：**
```json
{
  "customerId": 456
}
```

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": true
}
```

**业务规则：**
- 只能删除状态为"待确认"的追加服务申请
- 用户只能删除自己的追加服务申请
- 删除操作会同时删除相关的明细和优惠信息

---

## 2. 员工端接口

### 2.1 查询待确认的追加服务列表
**接口地址：** `GET /employee/additional-services/pending`  
**接口描述：** 员工查询待确认的追加服务申请列表  
**是否需要认证：** 是

**查询参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| employeeId | number | 是 | 员工ID |
| current | number | 否 | 当前页码，默认1 |
| pageSize | number | 否 | 每页数量，默认10 |

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "list": [
      {
        "id": 1,
        "sn": "ADD1703123456781234",
        "status": "pending_confirm",
        "originalPrice": 100.00,
        "totalFee": 90.00,
        "details": [
          {
            "id": 1,
            "serviceName": "深度清洁",
            "servicePrice": 50.00,
            "quantity": 2,
            "service": {
              "id": 1,
              "serviceName": "深度清洁"
            }
          }
        ],
        "orderDetail": {
          "id": 123,
          "order": {
            "id": 100,
            "sn": "1703123456781234",
            "customer": {
              "id": 456,
              "name": "张三",
              "phone": "13800138000"
            }
          }
        },
        "customer": {
          "id": 456,
          "name": "张三",
          "phone": "13800138000"
        },
        "createdAt": "2023-12-21T10:30:00.000Z"
      }
    ],
    "total": 1,
    "current": 1,
    "pageSize": 10
  }
}
```

### 2.2 员工确认追加服务
**接口地址：** `POST /order-details/{orderDetailId}/additional-services/{id}/confirm`
**接口描述：** 员工确认追加服务申请
**是否需要认证：** 是

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| orderDetailId | number | 是 | 订单详情ID |
| id | number | 是 | 追加服务订单ID |

**请求参数：**
```json
{
  "employeeId": 789
}
```

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "id": 1,
    "sn": "ADD1703123456781234",
    "status": "confirmed",
    "confirmTime": "2023-12-21T11:00:00.000Z"
  }
}
```

### 2.3 员工拒绝追加服务
**接口地址：** `POST /order-details/{orderDetailId}/additional-services/{id}/reject`
**接口描述：** 员工拒绝追加服务申请
**是否需要认证：** 是

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| orderDetailId | number | 是 | 订单详情ID |
| id | number | 是 | 追加服务订单ID |

**请求参数：**
```json
{
  "employeeId": 789,
  "rejectReason": "服务时间不够"
}
```

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "id": 1,
    "sn": "ADD1703123456781234",
    "status": "rejected",
    "rejectReason": "服务时间不够"
  }
}
```

### 2.4 查询追加服务列表（员工端复用）
**接口地址：** `GET /order-details/{orderDetailId}/additional-services`
**接口描述：** 员工查询指定订单详情的所有增项服务，包括主订单时选择的增项服务和后续追加的服务订单（与用户端相同接口）
**是否需要认证：** 是

**说明：**
- 员工端使用与用户端相同的接口查询增项服务列表
- 返回数据结构包含两部分：主订单增项服务 + 追加服务订单
- 员工可以看到完整的增项服务信息，便于服务时参考

### 2.5 查询追加服务详情（员工端复用）
**接口地址：** `GET /order-details/{orderDetailId}/additional-services/{id}`
**接口描述：** 员工查询指定追加服务的详细信息（与用户端相同接口）
**是否需要认证：** 是

**说明：** 员工端可以使用与用户端相同的接口查询追加服务详情

---

## 3. 管理端接口

### 3.1 订单列表（包含追加服务摘要信息）
**接口地址：** `GET /orders`
**接口描述：** 管理端订单列表，通过冗余字段显示追加服务基本信息
**是否需要认证：** 是

**查询参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| current | number | 否 | 当前页码，默认1 |
| pageSize | number | 否 | 每页数量，默认20 |
| includeAdditionalServices | boolean | 否 | 是否包含追加服务信息，默认true |
| ...其他原有订单查询参数 | | | |

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "list": [
      {
        "id": 100,
        "sn": "1703123456781234",
        "status": "服务中",
        "totalFee": 200.00,
        "customer": {
          "id": 456,
          "nickname": "张三",
          "phone": "13800138000"
        },
        "employee": {
          "id": 789,
          "name": "李师傅"
        },
        "hasAdditionalServices": true,
        "additionalServiceAmount": 180.00,
        "additionalServiceOriginalPrice": 200.00,
        "additionalServicesCompleted": false,
        "createdAt": "2023-12-21T09:00:00.000Z"
      }
    ],
    "total": 1,
    "current": 1,
    "pageSize": 20
  }
}
```

**说明：**
- 通过冗余字段快速获取追加服务基本信息：
  - `hasAdditionalServices`: 是否有追加服务
  - `additionalServiceAmount`: 追加服务总金额
  - `additionalServiceOriginalPrice`: 追加服务原价总金额
  - `additionalServicesCompleted`: 追加服务是否全部完成
- 如需详细信息，使用下面的专门接口查询

### 3.2 查询订单追加服务详情
**接口地址：** `GET /admin/orders/{orderId}/additional-services`
**接口描述：** 管理端查询指定订单的所有追加服务详细信息
**是否需要认证：** 是

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| orderId | number | 是 | 主订单ID |

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "orderId": 100,
    "orderSn": "1703123456781234",
    "additionalServiceSummary": {
      "totalCount": 2,
      "totalAmount": 180.00,
      "totalOriginalPrice": 200.00,
      "totalDeduction": 20.00,
      "paidCount": 1,
      "paidAmount": 90.00,
      "pendingCount": 1,
      "pendingAmount": 90.00
    },
    "additionalServices": [
      {
        "id": 1,
        "sn": "ADD1703123456781234",
        "status": "paid",
        "originalPrice": 100.00,
        "totalFee": 90.00,
        "cardDeduction": 0.00,
        "couponDeduction": 10.00,
        "payTime": "2023-12-21T12:00:00.000Z",
        "details": [
          {
            "serviceName": "深度清洁",
            "servicePrice": 50.00,
            "quantity": 2
          }
        ],
        "createdAt": "2023-12-21T10:30:00.000Z"
      },
      {
        "id": 2,
        "sn": "ADD1703123456781235",
        "status": "confirmed",
        "originalPrice": 100.00,
        "totalFee": 90.00,
        "cardDeduction": 10.00,
        "couponDeduction": 0.00,
        "confirmTime": "2023-12-21T11:30:00.000Z",
        "details": [
          {
            "serviceName": "除菌消毒",
            "servicePrice": 30.00,
            "quantity": 3
          }
        ],
        "createdAt": "2023-12-21T11:00:00.000Z"
      }
    ]
  }
}
```

### 3.3 追加服务订单列表
**接口地址：** `GET /admin/additional-services`
**接口描述：** 管理端查询追加服务订单列表，支持多维度筛选
**是否需要认证：** 是

**查询参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| current | number | 否 | 当前页码，默认1 |
| pageSize | number | 否 | 每页数量，默认20 |
| status | string | 否 | 状态筛选 |
| startDate | string | 否 | 开始日期，格式：YYYY-MM-DD |
| endDate | string | 否 | 结束日期，格式：YYYY-MM-DD |
| customerPhone | string | 否 | 客户手机号 |
| employeeId | number | 否 | 员工ID |
| orderSn | string | 否 | 主订单号 |
| additionalServiceSn | string | 否 | 追加服务订单号 |

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "list": [
      {
        "id": 1,
        "sn": "ADD1703123456781234",
        "status": "paid",
        "originalPrice": 100.00,
        "totalFee": 90.00,
        "cardDeduction": 0.00,
        "couponDeduction": 10.00,
        "payTime": "2023-12-21T12:00:00.000Z",
        "confirmTime": "2023-12-21T11:00:00.000Z",
        "mainOrder": {
          "id": 100,
          "sn": "1703123456781234",
          "status": "服务中",
          "customer": {
            "id": 456,
            "nickname": "张三",
            "phone": "13800138000"
          },
          "employee": {
            "id": 789,
            "name": "李师傅",
            "phone": "13900139000"
          }
        },
        "details": [
          {
            "serviceName": "深度清洁",
            "servicePrice": 50.00,
            "quantity": 2
          }
        ],
        "createdAt": "2023-12-21T10:30:00.000Z"
      }
    ],
    "total": 1,
    "current": 1,
    "pageSize": 20,
    "summary": {
      "totalCount": 1,
      "totalAmount": 90.00,
      "totalOriginalPrice": 100.00,
      "totalDeduction": 10.00
    }
  }
}
```

### 3.4 追加服务数据统计
**接口地址：** `GET /admin/additional-services/statistics`
**接口描述：** 管理端查询追加服务数据统计
**是否需要认证：** 是

**查询参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| startDate | string | 否 | 开始日期，格式：YYYY-MM-DD |
| endDate | string | 否 | 结束日期，格式：YYYY-MM-DD |
| groupBy | string | 否 | 分组方式：day-按天，month-按月，默认不分组 |

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "overview": {
      "totalOrders": 156,
      "totalAmount": 15600.00,
      "totalOriginalPrice": 18200.00,
      "totalDeduction": 2600.00,
      "averageOrderAmount": 100.00,
      "statusDistribution": {
        "pending_confirm": 12,
        "confirmed": 8,
        "paid": 120,
        "rejected": 16
      }
    },
    "trends": [],
    "topServices": [],
    "employeeRanking": []
  }
}
```

### 3.5 手动同步追加服务支付状态
**接口地址：** `POST /admin/additional-services/{id}/sync-payment-status`
**接口描述：** 管理端手动同步追加服务支付状态
**是否需要认证：** 是

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | number | 是 | 追加服务订单ID |

**请求参数：**
```json
{
  "operatorId": 123,
  "reason": "支付异常，手动同步状态"
}
```

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "success": true,
    "syncFromWechat": true,
    "message": "根据微信支付状态更新为已支付",
    "localStatus": "paid",
    "wechatStatus": "SUCCESS",
    "wechatTransactionId": "4200001234567890123456789",
    "payTime": "2023-12-21T10:30:00.000Z",
    "operator": {
      "id": 123,
      "name": "管理员"
    }
  }
}
```

### 3.6 追加服务退款处理
**接口地址：** `POST /admin/additional-services/{id}/refund`
**接口描述：** 管理端单独处理追加服务退款
**是否需要认证：** 是

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | number | 是 | 追加服务订单ID |

**请求参数：**
```json
{
  "operatorId": 123,
  "reason": "服务质量问题退款",
  "refundAmount": 90.00,
  "shouldRefundCoupons": true
}
```

**参数说明：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| operatorId | number | 是 | 操作员ID |
| reason | string | 是 | 退款原因 |
| refundAmount | number | 否 | 退款金额，不填则全额退款 |
| shouldRefundCoupons | boolean | 否 | 是否退回优惠券，默认true |

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "success": true,
    "refundAmount": 90.00,
    "refundStatus": "completed",
    "message": "追加服务退款成功"
  }
}
```

### 3.7 批量操作追加服务订单
**接口地址：** `POST /admin/additional-services/batch-operation`
**接口描述：** 管理端批量操作追加服务订单
**是否需要认证：** 是

**请求参数：**
```json
{
  "operation": "sync_payment_status",
  "orderIds": [1, 2, 3, 4, 5],
  "operatorId": 123,
  "reason": "批量同步支付状态"
}
```

**参数说明：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| operation | string | 是 | 操作类型：sync_payment_status-同步支付状态 |
| orderIds | array | 是 | 追加服务订单ID列表 |
| operatorId | number | 是 | 操作员ID |
| reason | string | 否 | 操作原因 |

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "totalCount": 5,
    "successCount": 4,
    "failedCount": 1,
    "results": [
      {
        "orderId": 1,
        "success": true,
        "message": "同步成功"
      },
      {
        "orderId": 2,
        "success": false,
        "message": "订单不存在"
      }
    ]
  }
}
```

---

## 4. 通用接口（多端使用）

### 4.1 获取微信支付参数
**接口地址：** `POST /wepay/jsapi`
**接口描述：** 获取追加服务订单的微信支付参数
**是否需要认证：** 是
**使用端：** 用户端、管理端

**请求参数：**
```json
{
  "appid": "wx1234567890abcdef",
  "sn": "ADD1703123456781234"
}
```

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "prepay_id": "wx123456789012345678901234567890"
  }
}
```

**说明：**
- 此接口与主订单支付使用相同的接口，系统会根据订单号前缀自动识别订单类型
- 追加服务订单号以"ADD"开头
- 返回的prepay_id用于调用微信支付

### 4.2 查询微信支付状态
**接口地址：** `GET /wepay/transactions/sn/{sn}`
**接口描述：** 查询微信支付状态，支持自动同步本地订单状态
**是否需要认证：** 是
**使用端：** 用户端、员工端、管理端

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| sn | string | 是 | 订单号（支持主订单、追加服务、权益卡、代金券） |

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "out_trade_no": "ADD1703123456781234",
    "transaction_id": "4200001234567890123456789",
    "trade_state": "SUCCESS",
    "trade_state_desc": "支付成功",
    "amount": {
      "total": 9000,
      "currency": "CNY"
    },
    "success_time": "2023-12-21T10:30:00+08:00"
  }
}
```

**说明：**
- 此接口会自动检查并同步本地订单状态
- 支持所有类型的订单号查询
- 返回微信支付原始数据

---

## 5. 主订单退款相关接口

### 5.1 申请退款
**接口地址：** `POST /customers/{customerId}/applyRefund/{sn}`
**接口描述：** 用户申请主订单退款
**是否需要认证：** 是
**使用端：** 用户端

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| customerId | number | 是 | 客户ID |
| sn | string | 是 | 主订单号 |

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": true
}
```

**说明：**
- 只有状态为"待服务"或"已出发"的订单才能申请退款
- 申请后订单状态变为"退款中"

### 5.2 审核退款
**接口地址：** `POST /orders/{sn}/auditRefund`
**接口描述：** 管理端审核主订单退款申请
**是否需要认证：** 是
**使用端：** 管理端

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| sn | string | 是 | 主订单号 |

**请求参数：**
```json
{
  "result": true,
  "reason": "同意退款",
  "money": 280.00
}
```

**参数说明：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| result | boolean | 是 | 审核结果，true为同意，false为拒绝 |
| reason | string | 否 | 审核原因 |
| money | number | 否 | 退款金额（审核通过时必填） |

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "sn": "1703123456781234",
    "status": "已退款",
    "refundAmount": 280.00,
    "additionalServiceRefunds": [
      {
        "additionalServiceOrderId": 1,
        "additionalServiceSn": "ADD1703123456781234",
        "refundAmount": 90.00,
        "couponsRefunded": true
      }
    ]
  }
}
```

### 5.3 取消订单
**接口地址：** `DELETE /customers/{customerId}/order/{orderId}`
**接口描述：** 用户取消主订单
**是否需要认证：** 是
**使用端：** 用户端

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| customerId | number | 是 | 客户ID |
| orderId | number | 是 | 主订单ID |

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": true
}
```

### 5.4 管理端取消订单
**接口地址：** `POST /orders/{orderId}/cancel`
**接口描述：** 管理端取消主订单
**是否需要认证：** 是
**使用端：** 管理端

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| orderId | number | 是 | 主订单ID |

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": true
}
```

---

## 6. 业务规则说明

### 6.1 主订单退款对追加服务的影响

#### 全额退款（退款金额 >= 主订单金额 + 追加服务金额）
1. **主订单处理**：
   - 退回主订单使用的优惠券/权益卡
   - 执行微信支付退款
   - 更新订单状态为"已退款"

2. **追加服务处理**：
   - 自动退款所有已支付的追加服务订单
   - 退回追加服务使用的优惠券/权益卡
   - 更新追加服务订单状态为"已退款"

3. **数据同步**：
   - 更新主订单的追加服务统计信息
   - 清除微信订阅信息
   - 广播取消订单消息

#### 部分退款（退款金额 < 主订单金额 + 追加服务金额）
1. **主订单处理**：
   - 不退回主订单使用的优惠券/权益卡
   - 执行微信支付退款
   - 更新订单状态为"已退款"

2. **追加服务处理**：
   - 不处理追加服务退款
   - 追加服务订单保持原状态

#### 取消订单
1. **主订单处理**：
   - 退回主订单使用的优惠券/权益卡
   - 更新订单状态为"已取消"

2. **追加服务处理**：
   - 自动退款所有已支付的追加服务订单
   - 退回追加服务使用的优惠券/权益卡（取消订单时总是退回）
   - 更新追加服务订单状态为"已退款"

### 6.2 状态流转规则
1. **待确认** → **已确认** → **待付款** → **已付款/服务中** → **已完成**
2. **待确认** → **已拒绝**
3. **已确认** → **已取消**
4. **待付款** → **已取消**
5. **已付款/服务中** → **退款中** → **已退款**

### 6.3 业务限制
1. 只有状态为"服务中"的订单才能申请追加服务
2. 员工只能确认/拒绝自己负责的订单的追加服务
3. 用户只能支付已确认的追加服务
4. 追加服务的退款需要通过主订单的退款流程处理

### 6.4 价格计算
1. 原价 = 各服务价格 × 数量的总和
2. 实际支付金额 = 原价 - 权益卡抵扣 - 代金券抵扣
3. 主订单的追加服务总金额会实时更新

### 6.5 数据一致性保障
- 使用事务确保主订单和追加服务退款的原子性
- 冗余字段自动更新，保持数据一致性
- 微信支付退款和本地状态更新同步进行

---

## 7. 前端集成建议

### 7.1 管理端订单列表集成
```javascript
// 利用冗余字段快速显示追加服务信息
const orders = await api.get('/orders', {
  params: { includeAdditionalServices: true }
});

orders.data.list.forEach(order => {
  if (order.hasAdditionalServices) {
    // 显示追加服务标识
    // 显示总金额：order.additionalServiceAmount
    // 显示原价：order.additionalServiceOriginalPrice
    // 显示完成状态：order.additionalServicesCompleted
  }
});
```

### 7.2 订单详情页面集成
```javascript
// 查询详细追加服务信息
const additionalServices = await api.get(`/admin/orders/${orderId}/additional-services`);

// 显示完整的追加服务列表和操作按钮
```

### 7.3 数据分析集成
```javascript
// 获取追加服务统计数据
const statistics = await api.get('/admin/additional-services/statistics');

// 合并到总体营收分析
const totalRevenue = mainOrderRevenue + statistics.data.overview.totalAmount;
```

---

## 8. 实现状态

### 8.1 已实现的接口
- ✅ 用户端接口：全部实现完成
- ✅ 员工端接口：全部实现完成
- ✅ 管理端接口：全部实现完成
- ✅ 支付流程：完整优化完成
- ✅ 退款流程：主订单退款已完整处理追加服务

### 8.2 核心功能特性
- ✅ 0元订单自动支付处理
- ✅ 微信支付集成（与主订单一致）
- ✅ 支付回调自动状态更新
- ✅ 支付状态异常自动修复机制
- ✅ 主订单追加服务信息实时更新
- ✅ 冗余字段自动维护
- ✅ 优惠券使用和退回逻辑

所有核心功能已实现完成，可以开始前端开发和集成工作。
