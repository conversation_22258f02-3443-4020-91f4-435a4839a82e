# 增项服务计时状态修复说明

## 问题描述

用户反馈：前端通过 `/order-details/183/additional-services` 接口查询增项服务情况，前端渲染为"已开始"，但用户点击完成调用 `/employee/service-duration/end` 却提示"服务尚未开始"。

## 问题根因分析

### 数据流程问题
1. **接口返回数据**：`/order-details/{orderDetailId}/additional-services` 接口返回增项服务基础信息，包括 `needDurationTracking: true`
2. **前端判断错误**：前端错误地将 `needDurationTracking: true`（需要计时）理解为"已开始计时"
3. **实际状态**：在 `ServiceDurationRecord` 表中，该增项服务可能：
   - 没有创建计时记录
   - 创建了记录但 `startTime` 为 `null`（无效记录）
4. **结果冲突**：前端显示"已开始"，后端检查发现"尚未开始"

### 核心问题
**前端缺少真实的计时状态信息**，仅凭 `needDurationTracking` 字段无法准确判断服务是否真的已经开始计时。

## 解决方案

### 1. 增强接口返回数据
修改 `/order-details/{orderDetailId}/additional-services` 接口，为每个增项服务添加真实的计时状态信息。

### 2. 新增字段说明
为每个增项服务添加以下字段：
- `durationStatus`: 计时状态
  - `'not_started'`: 未开始（没有记录或 startTime 为空）
  - `'in_progress'`: 进行中（有 startTime 但没有 endTime）
  - `'completed'`: 已完成（有 startTime 和 endTime）
- `recordId`: 计时记录ID（用于调用结束接口）

### 3. 状态判断逻辑
```typescript
private getDurationStatus(durationRecord: any) {
  if (!durationRecord) {
    return 'not_started'; // 未开始
  }
  if (!durationRecord.startTime) {
    return 'not_started'; // 有记录但未开始（无效记录）
  }
  if (!durationRecord.endTime) {
    return 'in_progress'; // 进行中
  }
  return 'completed'; // 已完成
}
```

## 修改内容

### 文件：`src/controller/additional-service-order.controller.ts`

#### 1. 新增导入
```typescript
import { ServiceDurationRecord } from '../entity/service-duration-record.entity';
```

#### 2. 修改接口返回逻辑
- 查询每个增项服务的实际计时记录
- 根据计时记录判断真实状态
- 为前端提供准确的状态信息

#### 3. 新增辅助方法
- `getDurationRecordForOriginalService()`: 查询主订单增项服务的计时记录
- `getDurationRecordForAdditionalService()`: 查询追加服务增项服务的计时记录
- `getDurationStatus()`: 根据计时记录判断服务状态

## 接口变更

### 修改前的返回数据
```json
{
  "originalAdditionalServices": [
    {
      "id": 4,
      "name": "全身烘干",
      "needDurationTracking": true,
      "price": "120.00",
      "type": "PJ_MAO_HU_LI"
    }
  ]
}
```

### 修改后的返回数据
```json
{
  "originalAdditionalServices": [
    {
      "id": 4,
      "name": "全身烘干",
      "needDurationTracking": true,
      "price": "120.00",
      "type": "PJ_MAO_HU_LI",
      "durationStatus": "not_started",
      "recordId": null
    }
  ]
}
```

## 前端适配建议

### 1. 状态判断逻辑
```javascript
// 修改前（错误）
const isStarted = service.needDurationTracking;

// 修改后（正确）
const isStarted = service.durationStatus === 'in_progress';
const isCompleted = service.durationStatus === 'completed';
const canStart = service.needDurationTracking && service.durationStatus === 'not_started';
const canEnd = service.durationStatus === 'in_progress' && service.recordId;
```

### 2. 按钮状态控制
```javascript
// 开始按钮
const showStartButton = service.needDurationTracking && service.durationStatus === 'not_started';

// 完成按钮
const showEndButton = service.durationStatus === 'in_progress' && service.recordId;

// 状态显示
const statusText = {
  'not_started': '未开始',
  'in_progress': '进行中',
  'completed': '已完成'
}[service.durationStatus];
```

## 影响范围

### 后端接口
- `GET /order-details/{orderDetailId}/additional-services` - 返回数据结构增强

### 前端页面
- 增项服务列表页面需要更新状态判断逻辑
- 服务操作按钮的显示/隐藏逻辑需要调整

## 测试建议

1. **未开始状态**：增项服务配置了 `needDurationTracking: true` 但没有计时记录
2. **进行中状态**：增项服务有计时记录且 `startTime` 不为空，`endTime` 为空
3. **已完成状态**：增项服务有计时记录且 `startTime` 和 `endTime` 都不为空
4. **无效记录**：增项服务有计时记录但 `startTime` 为空（应显示为未开始）

## 预期效果

修复后，前端将能够：
1. 准确显示增项服务的真实计时状态
2. 正确控制开始/完成按钮的显示
3. 避免"前端显示已开始，后端提示未开始"的数据不一致问题
4. 提供更好的用户体验
