# 订单模块接口文档

## 统一返回格式说明

### 成功响应格式
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    // 具体业务数据
  }
}
```

### 错误响应格式
```json
{
  "errCode": 400, // 错误码，自定义错误为status值，系统错误为500
  "msg": "错误信息"
}
```

## 订单状态枚举
```typescript
enum OrderStatus {
  待付款 = '待付款',
  待接单 = '待接单', 
  待服务 = '待服务',
  已出发 = '已出发',
  服务中 = '服务中',
  已完成 = '已完成',
  已评价 = '已评价',
  已取消 = '已取消',
  退款中 = '退款中',
  已退款 = '已退款'
}
```

---

## 1. 服务订单接口

### 1.1 查询所有订单列表
**接口地址：** `GET /orders`  
**接口描述：** 查询所有订单列表，支持分页和筛选  
**是否需要认证：** 是

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| current | number | 否 | 当前页码，默认1 |
| pageSize | number | 否 | 每页数量，默认10 |
| phone | string | 否 | 客户手机号筛选 |
| employeename | string | 否 | 员工姓名筛选（模糊查询） |
| status | string | 否 | 订单状态筛选 |
| filter | string | 否 | JSON格式的筛选条件 |

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK", 
  "data": {
    "list": [
      {
        "id": 1,
        "sn": "ORD20240101001",
        "customerId": 1,
        "employeeId": 1,
        "status": "待接单",
        "isExpired": false,
        "orderTime": "2024-01-01T10:00:00.000Z",
        "serviceTime": "2024-01-01T14:00:00.000Z",
        "address": "北京市朝阳区xxx",
        "longitude": 116.123456,
        "latitude": 39.123456,
        "addressDetail": "xxx小区1号楼",
        "addressRemark": "门口有保安",
        "originalPrice": 100.00,
        "totalFee": 80.00,
        "cardDeduction": 20.00,
        "couponDeduction": 0.00,
        "customer": {
          "id": 1,
          "nickname": "张三",
          "phone": "13800138000"
        },
        "employee": {
          "id": 1,
          "name": "李四",
          "phone": "13900139000"
        },
        "orderDetails": [
          {
            "id": 1,
            "serviceId": 1,
            "serviceName": "宠物洗护",
            "servicePrice": 100.00,
            "petId": 1,
            "petName": "小白",
            "petType": "狗",
            "petBreed": "金毛",
            "service": {
              "id": 1,
              "serviceName": "宠物洗护",
              "serviceType": {
                "id": 1,
                "name": "洗护服务"
              }
            },
            "pet": {
              "id": 1,
              "name": "小白",
              "type": "狗",
              "breed": "金毛"
            },
            "additionalServices": []
          }
        ]
      }
    ],
    "total": 100
  }
}
```

### 1.2 获取订单日志
**接口地址：** `GET /orders/{orderId}/logs`  
**接口描述：** 获取指定订单的变更日志  
**是否需要认证：** 是

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| orderId | number | 是 | 订单ID |

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": [
    {
      "id": 1,
      "orderId": 1,
      "changeType": "下单",
      "description": "客户下单",
      "createdAt": "2024-01-01T10:00:00.000Z"
    },
    {
      "id": 2,
      "orderId": 1,
      "changeType": "付款",
      "description": "微信支付回调：支付成功",
      "createdAt": "2024-01-01T10:05:00.000Z"
    }
  ]
}
```

### 1.3 查询可接单列表
**接口地址：** `GET /orders/{userId}`  
**接口描述：** 查询指定用户的可接单列表  
**是否需要认证：** 是

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| userId | number | 是 | 用户ID |

**查询参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| current | number | 否 | 当前页码 |
| pageSize | number | 否 | 每页数量 |
| type | string | 否 | 订单类型 |

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "list": [
      {
        "id": 1,
        "sn": "ORD20240101001",
        "status": "待接单",
        "orderTime": "2024-01-01T10:00:00.000Z",
        "serviceTime": "2024-01-01T14:00:00.000Z",
        "address": "北京市朝阳区xxx",
        "totalFee": 80.00
      }
    ],
    "total": 10
  }
}
```

### 1.4 查询员工订单列表
**接口地址：** `GET /orders/employee/{employeeId}`
**接口描述：** 按状态查询员工名下的订单列表
**是否需要认证：** 是

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| employeeId | number | 是 | 员工ID |

**查询参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| status | string | 是 | 订单状态，多个状态用逗号分隔 |
| current | number | 否 | 当前页码 |
| pageSize | number | 否 | 每页数量 |

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "list": [
      {
        "id": 1,
        "sn": "ORD20240101001",
        "status": "待服务",
        "orderTime": "2024-01-01T10:00:00.000Z",
        "serviceTime": "2024-01-01T14:00:00.000Z",
        "address": "北京市朝阳区xxx",
        "totalFee": 80.00
      }
    ],
    "total": 5
  }
}
```

## 2. 订单操作接口

### 2.1 接单
**接口地址：** `POST /orders/{orderId}/accept`
**接口描述：** 员工接单操作
**是否需要认证：** 是

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| orderId | number | 是 | 订单ID |

**请求体：**
```json
{
  "employeeId": 1
}
```

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| employeeId | number | 是 | 员工ID |

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "id": 1,
    "status": "待服务",
    "employeeId": 1
  }
}
```

### 2.2 开始服务
**接口地址：** `POST /orders/{orderId}/start`
**接口描述：** 员工开始服务操作，可上传服务前照片
**是否需要认证：** 是

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| orderId | number | 是 | 订单ID |

**请求体：**
```json
{
  "employeeId": 1,
  "beforePhotos": [
    "https://example.com/photo1.jpg",
    "https://example.com/photo2.jpg"
  ]
}
```

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| employeeId | number | 是 | 员工ID |
| beforePhotos | string[] | 否 | 服务前照片URL数组，最多9张 |

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "id": 1,
    "status": "服务中",
    "employeeId": 1
  }
}
```

### 2.3 完成订单
**接口地址：** `POST /orders/{orderId}/complete`
**接口描述：** 员工完成订单操作，可上传服务后照片
**是否需要认证：** 是

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| orderId | number | 是 | 订单ID |

**请求体：**
```json
{
  "employeeId": 1,
  "afterPhotos": [
    "https://example.com/after1.jpg",
    "https://example.com/after2.jpg"
  ]
}
```

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| employeeId | number | 是 | 员工ID |
| afterPhotos | string[] | 否 | 服务后照片URL数组，最多9张 |

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "id": 1,
    "status": "已完成",
    "employeeId": 1
  }
}
```

### 2.4 取消订单
**接口地址：** `POST /orders/{orderId}/cancel`
**接口描述：** 取消订单操作（后台取消）
**是否需要认证：** 是

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| orderId | number | 是 | 订单ID |

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "id": 1,
    "status": "已取消"
  }
}
```

### 2.5 删除订单
**接口地址：** `DELETE /orders/{orderId}`
**接口描述：** 删除订单
**是否需要认证：** 是

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| orderId | number | 是 | 订单ID |

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": true
}
```

### 2.6 审核退款
**接口地址：** `POST /orders/{sn}/auditRefund`
**接口描述：** 审核订单退款申请
**是否需要认证：** 是

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| sn | string | 是 | 订单编号 |

**请求体：**
```json
{
  "result": true,
  "reason": "同意退款",
  "money": 80.00
}
```

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| result | boolean | 是 | 审核结果，true为同意，false为拒绝 |
| reason | string | 否 | 审核原因 |
| money | number | 否 | 退款金额 |

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "sn": "ORD20240101001",
    "status": "已退款",
    "refundAmount": 80.00
  }
}
```

### 2.7 修改服务地址
**接口地址：** `PUT /orders/{orderId}/updateServiceAddress`
**接口描述：** 修改订单的服务地址信息
**是否需要认证：** 是
**适用端：** 管理端/员工端/用户端

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| orderId | number | 是 | 订单ID |

**请求体：**
```json
{
  "address": "北京市朝阳区xxx街道",
  "addressDetail": "xxx小区1号楼101室",
  "longitude": 116.123456,
  "latitude": 39.123456,
  "addressRemark": "门口有保安，请提前联系",
  "addressId": 1,
  "employeeId": 1,
  "userType": "admin"
}
```

**请求参数说明：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| address | string | 否 | 服务地址（最大255字符） |
| addressDetail | string | 否 | 服务地址详情（最大255字符） |
| longitude | number | 否 | 经度（-180到180之间） |
| latitude | number | 否 | 纬度（-90到90之间） |
| addressRemark | string | 否 | 服务地址备注（最大255字符） |
| addressId | number/null | 否 | 关联的客户地址ID，null表示取消关联 |
| employeeId | number | 否 | 操作员工ID（用于记录日志） |
| userType | string | 否 | 用户类型：admin/employee/customer，默认admin |

**权限说明：**
- **管理端（admin）**：可在任何订单状态下修改服务地址
- **员工端（employee）**：只能在出发前（待付款、待接单、待服务）修改服务地址
- **用户端（customer）**：只能在出发前（待付款、待接单、待服务）修改服务地址

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": true
}
```

**错误响应示例：**
```json
{
  "errCode": 400,
  "msg": "当前订单状态不允许修改服务地址"
}
```

## 3. 服务照片管理接口

### 3.1 员工端接口

#### 3.1.1 设置服务前照片（完整替换）
**接口地址：** `POST /service-photos/set-before-photos`
**接口描述：** 员工设置服务前照片，完整替换模式，支持添加/删除/重置
**是否需要认证：** 是
**适用端：** 员工端

**请求体：**
```json
{
  "orderId": 1,
  "employeeId": 1,
  "photoUrls": [
    "https://example.com/before1.jpg",
    "https://example.com/before2.jpg"
  ]
}
```

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| orderId | number | 是 | 订单ID |
| employeeId | number | 是 | 员工ID |
| photoUrls | string[] | 是 | 照片URL数组，最多9张，传空数组表示清空 |

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "id": 1,
    "orderId": 1,
    "employeeId": 1,
    "beforePhotos": [
      "https://example.com/before1.jpg",
      "https://example.com/before2.jpg"
    ],
    "beforePhotoTime": "2024-01-01T14:00:00.000Z"
  }
}
```

#### 3.1.2 批量上传服务前照片（增量模式）
**接口地址：** `POST /service-photos/upload-before-photos`
**接口描述：** 员工批量上传服务前照片，增量模式，最多9张
**是否需要认证：** 是
**适用端：** 员工端

**请求体：**
```json
{
  "orderId": 1,
  "employeeId": 1,
  "photoUrls": [
    "https://example.com/before3.jpg",
    "https://example.com/before4.jpg"
  ]
}
```

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| orderId | number | 是 | 订单ID |
| employeeId | number | 是 | 员工ID |
| photoUrls | string[] | 是 | 新增照片URL数组，会与现有照片合并去重 |

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "id": 1,
    "orderId": 1,
    "employeeId": 1,
    "beforePhotos": [
      "https://example.com/before1.jpg",
      "https://example.com/before2.jpg",
      "https://example.com/before3.jpg",
      "https://example.com/before4.jpg"
    ],
    "beforePhotoTime": "2024-01-01T14:05:00.000Z"
  }
}
```

#### 3.1.3 上传单张服务前照片
**接口地址：** `POST /service-photos/upload-before`
**接口描述：** 员工上传单张服务前照片
**是否需要认证：** 是
**适用端：** 员工端

**请求体：**
```json
{
  "orderId": 1,
  "employeeId": 1,
  "photoUrl": "https://example.com/before5.jpg"
}
```

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| orderId | number | 是 | 订单ID |
| employeeId | number | 是 | 员工ID |
| photoUrl | string | 是 | 照片URL |

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "id": 1,
    "orderId": 1,
    "employeeId": 1,
    "beforePhotos": [
      "https://example.com/before1.jpg",
      "https://example.com/before5.jpg"
    ]
  }
}
```

#### 3.1.4 设置服务后照片（完整替换）
**接口地址：** `POST /service-photos/set-after-photos`
**接口描述：** 员工设置服务后照片，完整替换模式，支持添加/删除/重置
**是否需要认证：** 是
**适用端：** 员工端

**请求体：**
```json
{
  "orderId": 1,
  "employeeId": 1,
  "photoUrls": [
    "https://example.com/after1.jpg",
    "https://example.com/after2.jpg"
  ]
}
```

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| orderId | number | 是 | 订单ID |
| employeeId | number | 是 | 员工ID |
| photoUrls | string[] | 是 | 照片URL数组，最多9张，传空数组表示清空 |

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "id": 1,
    "orderId": 1,
    "employeeId": 1,
    "afterPhotos": [
      "https://example.com/after1.jpg",
      "https://example.com/after2.jpg"
    ],
    "afterPhotoTime": "2024-01-01T16:00:00.000Z"
  }
}
```

#### 3.1.5 批量上传服务后照片（增量模式）
**接口地址：** `POST /service-photos/upload-after-photos`
**接口描述：** 员工批量上传服务后照片，增量模式，最多9张
**是否需要认证：** 是
**适用端：** 员工端

**请求体：**
```json
{
  "orderId": 1,
  "employeeId": 1,
  "photoUrls": [
    "https://example.com/after3.jpg",
    "https://example.com/after4.jpg"
  ]
}
```

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| orderId | number | 是 | 订单ID |
| employeeId | number | 是 | 员工ID |
| photoUrls | string[] | 是 | 新增照片URL数组，会与现有照片合并去重 |

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "id": 1,
    "orderId": 1,
    "employeeId": 1,
    "afterPhotos": [
      "https://example.com/after1.jpg",
      "https://example.com/after2.jpg",
      "https://example.com/after3.jpg",
      "https://example.com/after4.jpg"
    ],
    "afterPhotoTime": "2024-01-01T16:05:00.000Z"
  }
}
```

#### 3.1.6 上传单张服务后照片
**接口地址：** `POST /service-photos/upload-after`
**接口描述：** 员工上传单张服务后照片
**是否需要认证：** 是
**适用端：** 员工端

**请求体：**
```json
{
  "orderId": 1,
  "employeeId": 1,
  "photoUrl": "https://example.com/after5.jpg"
}
```

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| orderId | number | 是 | 订单ID |
| employeeId | number | 是 | 员工ID |
| photoUrl | string | 是 | 照片URL |

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "id": 1,
    "orderId": 1,
    "employeeId": 1,
    "afterPhotos": [
      "https://example.com/after1.jpg",
      "https://example.com/after5.jpg"
    ]
  }
}
```

#### 3.1.7 删除指定的服务前照片
**接口地址：** `POST /service-photos/remove-before-photos`
**接口描述：** 员工删除指定的服务前照片
**是否需要认证：** 是
**适用端：** 员工端

**请求体：**
```json
{
  "orderId": 1,
  "employeeId": 1,
  "photoUrls": [
    "https://example.com/before1.jpg",
    "https://example.com/before2.jpg"
  ]
}
```

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| orderId | number | 是 | 订单ID |
| employeeId | number | 是 | 员工ID |
| photoUrls | string[] | 是 | 要删除的照片URL数组 |

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "id": 1,
    "orderId": 1,
    "employeeId": 1,
    "beforePhotos": [
      "https://example.com/before3.jpg"
    ]
  }
}
```

#### 3.1.8 删除指定的服务后照片
**接口地址：** `POST /service-photos/remove-after-photos`
**接口描述：** 员工删除指定的服务后照片
**是否需要认证：** 是
**适用端：** 员工端

**请求体：**
```json
{
  "orderId": 1,
  "employeeId": 1,
  "photoUrls": [
    "https://example.com/after1.jpg"
  ]
}
```

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| orderId | number | 是 | 订单ID |
| employeeId | number | 是 | 员工ID |
| photoUrls | string[] | 是 | 要删除的照片URL数组 |

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "id": 1,
    "orderId": 1,
    "employeeId": 1,
    "afterPhotos": [
      "https://example.com/after2.jpg"
    ]
  }
}
```

#### 3.1.9 检查是否已上传服务前照片
**接口地址：** `GET /service-photos/check/{orderId}/{employeeId}/before`
**接口描述：** 员工检查是否已上传服务前照片
**是否需要认证：** 是
**适用端：** 员工端

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| orderId | number | 是 | 订单ID |
| employeeId | number | 是 | 员工ID |

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "hasPhoto": true
  }
}
```

#### 3.1.10 检查是否已上传服务后照片
**接口地址：** `GET /service-photos/check/{orderId}/{employeeId}/after`
**接口描述：** 员工检查是否已上传服务后照片
**是否需要认证：** 是
**适用端：** 员工端

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| orderId | number | 是 | 订单ID |
| employeeId | number | 是 | 员工ID |

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "hasPhoto": false
  }
}
```

#### 3.1.11 获取照片统计信息
**接口地址：** `GET /service-photos/statistics/{orderId}/{employeeId}`
**接口描述：** 员工获取照片统计信息
**是否需要认证：** 是
**适用端：** 员工端

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| orderId | number | 是 | 订单ID |
| employeeId | number | 是 | 员工ID |

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "beforePhotoCount": 3,
    "afterPhotoCount": 2,
    "hasBeforePhoto": true,
    "hasAfterPhoto": true,
    "beforePhotoTime": "2024-01-01T14:00:00.000Z",
    "afterPhotoTime": "2024-01-01T16:00:00.000Z"
  }
}
```

### 3.2 用户端接口

#### 3.2.1 查询订单的服务照片
**接口地址：** `GET /orders/{orderId}/service-photos`
**接口描述：** 用户查询订单的服务照片
**是否需要认证：** 是
**适用端：** 用户端

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| orderId | number | 是 | 订单ID |

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "id": 1,
    "orderId": 1,
    "employeeId": 1,
    "beforePhotos": [
      "https://example.com/before1.jpg",
      "https://example.com/before2.jpg"
    ],
    "beforePhotoTime": "2024-01-01T14:00:00.000Z",
    "afterPhotos": [
      "https://example.com/after1.jpg",
      "https://example.com/after2.jpg"
    ],
    "afterPhotoTime": "2024-01-01T16:00:00.000Z",
    "employee": {
      "id": 1,
      "name": "李四",
      "phone": "13900139000"
    }
  }
}
```

### 3.3 管理端接口

#### 3.3.1 查询服务照片列表
**接口地址：** `GET /service-photos`
**接口描述：** 管理端查询服务照片列表，支持分页和筛选
**是否需要认证：** 是
**适用端：** 管理端

**查询参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| current | number | 否 | 当前页码 |
| pageSize | number | 否 | 每页数量 |
| orderId | number | 否 | 订单ID筛选 |
| employeeId | number | 否 | 员工ID筛选 |

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "list": [
      {
        "id": 1,
        "orderId": 1,
        "employeeId": 1,
        "beforePhotos": ["https://example.com/before1.jpg"],
        "beforePhotoTime": "2024-01-01T14:00:00.000Z",
        "afterPhotos": ["https://example.com/after1.jpg"],
        "afterPhotoTime": "2024-01-01T16:00:00.000Z",
        "order": {
          "id": 1,
          "sn": "ORD20240101001",
          "customer": {
            "id": 1,
            "nickname": "张三"
          }
        },
        "employee": {
          "id": 1,
          "name": "李四"
        }
      }
    ],
    "total": 10
  }
}
```

#### 3.3.2 按ID查询服务照片
**接口地址：** `GET /service-photos/{id}`
**接口描述：** 管理端按ID查询服务照片详情
**是否需要认证：** 是
**适用端：** 管理端

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | number | 是 | 服务照片ID |

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "id": 1,
    "orderId": 1,
    "employeeId": 1,
    "beforePhotos": ["https://example.com/before1.jpg"],
    "beforePhotoTime": "2024-01-01T14:00:00.000Z",
    "afterPhotos": ["https://example.com/after1.jpg"],
    "afterPhotoTime": "2024-01-01T16:00:00.000Z"
  }
}
```

#### 3.3.3 查询员工的服务照片列表
**接口地址：** `GET /service-photos/employee/{employeeId}`
**接口描述：** 管理端查询指定员工的服务照片列表
**是否需要认证：** 是
**适用端：** 管理端

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| employeeId | number | 是 | 员工ID |

**查询参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| current | number | 否 | 当前页码，默认1 |
| pageSize | number | 否 | 每页数量，默认10 |

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "list": [
      {
        "id": 1,
        "orderId": 1,
        "beforePhotos": ["https://example.com/before1.jpg"],
        "afterPhotos": ["https://example.com/after1.jpg"],
        "order": {
          "id": 1,
          "sn": "ORD20240101001"
        }
      }
    ],
    "total": 5
  }
}
```

## 4. 客户订单接口

### 4.1 获取客户订单详情
**接口地址：** `GET /customers/{id}/order/{orderId}`
**接口描述：** 获取指定客户的订单详情
**是否需要认证：** 是

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | number | 是 | 客户ID |
| orderId | number | 是 | 订单ID |

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "id": 1,
    "sn": "ORD20240101001",
    "status": "已完成",
    "orderTime": "2024-01-01T10:00:00.000Z",
    "serviceTime": "2024-01-01T14:00:00.000Z",
    "address": "北京市朝阳区xxx",
    "totalFee": 80.00,
    "orderDetails": [
      {
        "id": 1,
        "serviceName": "宠物洗护",
        "servicePrice": 100.00,
        "petName": "小白"
      }
    ]
  }
}
```

### 4.2 获取订单状态
**接口地址：** `GET /customers/{customerId}/order/{sn}/status`
**接口描述：** 根据订单编号获取订单状态
**是否需要认证：** 是

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| customerId | number | 是 | 客户ID |
| sn | string | 是 | 订单编号 |

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "sn": "ORD20240101001",
    "status": "已完成",
    "orderTime": "2024-01-01T10:00:00.000Z"
  }
}
```

### 4.3 创建订单
**接口地址：** `POST /customers/{customerId}/order`
**接口描述：** 为指定客户创建订单
**是否需要认证：** 是

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| customerId | number | 是 | 客户ID |

**请求体：**
```json
{
  "orderTime": "2024-01-01T10:00:00.000Z",
  "serviceTime": "2024-01-01T14:00:00.000Z",
  "originalPrice": 100.00,
  "totalFee": 80.00,
  "cardDeduction": 20.00,
  "couponDeduction": 0.00,
  "orderDetails": [
    {
      "serviceId": 1,
      "petId": 1,
      "servicePrice": 100.00,
      "userRemark": "请轻柔一些，宠物比较敏感",
      "additionalServiceIds": [1, 2]
    }
  ],
  "discountInfos": [
    {
      "discountType": "membership_card",
      "discountId": 1,
      "discountAmount": 20.00
    }
  ]
}
```

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "id": 1,
    "sn": "ORD20240101001",
    "status": "待付款",
    "totalFee": 80.00,
    "prepay_id": "wx123456789"
  }
}
```

## 5. 权益卡订单接口

### 5.1 查询所有权益卡订单
**接口地址：** `GET /membership-card-orders/all`
**接口描述：** 查询所有权益卡订单列表（管理端）
**是否需要认证：** 是

**查询参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| status | string | 否 | 订单状态，多个状态用逗号分隔 |
| current | number | 否 | 当前页码 |
| pageSize | number | 否 | 每页数量 |

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "list": [
      {
        "id": 1,
        "sn": "MC20240101001",
        "customerId": 1,
        "cardTypeId": 1,
        "status": "已支付",
        "totalAmount": 200.00,
        "customer": {
          "id": 1,
          "nickname": "张三",
          "phone": "13800138000"
        },
        "cardType": {
          "id": 1,
          "name": "洗护折扣卡",
          "type": "discount",
          "discount": 0.8
        }
      }
    ],
    "total": 50
  }
}
```

### 5.2 查询客户权益卡订单
**接口地址：** `GET /membership-card-orders`
**接口描述：** 查询指定客户的权益卡订单列表
**是否需要认证：** 是

**查询参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| customerId | number | 是 | 客户ID |
| status | string | 否 | 订单状态，多个状态用逗号分隔 |
| current | number | 否 | 当前页码 |
| pageSize | number | 否 | 每页数量 |

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "list": [
      {
        "id": 1,
        "sn": "MC20240101001",
        "cardTypeId": 1,
        "status": "已支付",
        "totalAmount": 200.00,
        "cardType": {
          "id": 1,
          "name": "洗护折扣卡",
          "type": "discount"
        }
      }
    ],
    "total": 5
  }
}
```

### 5.3 按ID查询权益卡订单
**接口地址：** `GET /membership-card-orders/{id}`
**接口描述：** 按ID查询权益卡订单详情
**是否需要认证：** 是

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | number | 是 | 权益卡订单ID |

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "id": 1,
    "sn": "MC20240101001",
    "customerId": 1,
    "cardTypeId": 1,
    "status": "已支付",
    "totalAmount": 200.00,
    "payTime": "2024-01-01T10:05:00.000Z",
    "customer": {
      "id": 1,
      "nickname": "张三",
      "phone": "13800138000"
    },
    "cardType": {
      "id": 1,
      "name": "洗护折扣卡",
      "type": "discount",
      "discount": 0.8,
      "price": 200.00
    }
  }
}
```

## 6. 代金券订单接口

### 6.1 查询客户代金券订单
**接口地址：** `GET /coupon-orders`
**接口描述：** 查询指定客户的代金券订单列表
**是否需要认证：** 是

**查询参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| customerId | number | 是 | 客户ID |
| status | string | 否 | 订单状态，多个状态用逗号分隔 |
| current | number | 否 | 当前页码 |
| pageSize | number | 否 | 每页数量 |

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "list": [
      {
        "id": 1,
        "sn": "CP20240101001",
        "couponId": 1,
        "status": "已支付",
        "totalAmount": 50.00,
        "quantity": 1,
        "coupon": {
          "id": 1,
          "name": "50元代金券",
          "amount": 50.00,
          "price": 45.00
        }
      }
    ],
    "total": 3
  }
}
```

### 6.2 按ID查询代金券订单
**接口地址：** `GET /coupon-orders/{id}`
**接口描述：** 按ID查询代金券订单详情
**是否需要认证：** 是

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | number | 是 | 代金券订单ID |

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "id": 1,
    "sn": "CP20240101001",
    "customerId": 1,
    "couponId": 1,
    "status": "已支付",
    "totalAmount": 50.00,
    "quantity": 1,
    "payTime": "2024-01-01T10:05:00.000Z",
    "customer": {
      "id": 1,
      "nickname": "张三",
      "phone": "13800138000"
    },
    "coupon": {
      "id": 1,
      "name": "50元代金券",
      "amount": 50.00,
      "price": 45.00,
      "validDays": 30
    }
  }
}
```

## 7. 错误码说明

| 错误码 | 说明 |
|--------|------|
| 0 | 成功 |
| 400 | 请求参数错误 |
| 401 | 未登录或登录已过期 |
| 403 | 无权限访问 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

## 8. 注意事项

1. 所有接口都经过统一的格式化中间件处理，成功时返回 `{errCode: 0, msg: "OK", data: 实际数据}`
2. 错误时返回 `{errCode: 错误码, msg: "错误信息"}`，不包含data字段
3. 分页参数 `current` 和 `pageSize` 在大部分列表接口中都支持
4. 订单状态变更会自动记录到变更日志中
5. 照片上传需要先通过文件上传接口获取URL，再传递给相关接口
6. 所有时间字段都使用ISO 8601格式（如：2024-01-01T10:00:00.000Z）
7. 金额字段都使用number类型，保留两位小数
