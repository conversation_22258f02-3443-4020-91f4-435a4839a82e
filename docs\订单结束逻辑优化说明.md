# 订单结束逻辑优化说明

## 优化背景

原有系统中存在两种触发订单完成的场景，逻辑不够统一：
1. **主动完成订单**：`/orders/{orderId}/complete` 
2. **服务结束计时触发**：`/employee/service-duration/end` 

## 优化目标

统一两种场景的标准，确保：
1. 都必须考虑是否存在未付款的增项服务，若存在，则总订单不能结束
2. 主订单完成时：主服务必然触发结束计时，增项服务只有已经开始的才会触发结束计时
3. 增项服务结束时：检查是否满足触发主订单结束的条件（无未付款增项、无未开始的需要计时增项、且主服务和所有增项均为完成状态）

## 优化内容

### 1. 统一未付款增项服务检查逻辑

**文件**: `src/service/order.service.ts`
- 将 `checkUnpaidAdditionalServices` 方法改为 public，供其他服务共享使用
- 确保两种场景使用相同的检查逻辑

**文件**: `src/service/service-duration-record.service.ts`
- 删除重复的 `checkUnpaidAdditionalServices` 方法
- 调用 `orderService.checkUnpaidAdditionalServices()` 确保逻辑一致性

### 2. 优化主订单完成时的服务结束逻辑

**文件**: `src/service/order.service.ts` - `handleServiceDurationOnOrderComplete` 方法

**优化前**：
- 对所有未结束的服务记录，有开始时间的就结束，没有开始时间的跳过

**优化后**：
- **主服务**：必然触发结束计时（有开始时间但没有结束时间的，立即结束）
- **增项服务**：只有已经开始的才会触发结束计时（没有开始时间的，跳过处理）

```typescript
// 主服务必然触发结束计时，增项服务只有已经开始的才会触发结束计时
const isMainService = record.recordType === 'main_service';
const shouldEndService = isMainService || record.startTime;

if (shouldEndService && record.startTime) {
  // 有开始时间的，立即结束
  // ...结束逻辑
} else {
  // 没有开始时间的，跳过处理（主要是增项服务）
  // ...跳过逻辑
}
```

### 3. 增强服务结束计时触发的订单完成检查

**文件**: `src/service/service-duration-record.service.ts` - `checkAndCompleteOrderIfAllServicesFinished` 方法

**检查条件**（必须同时满足）：
1. 所有主服务必须完成
2. 不能存在未付款的附加服务（必须条件）
3. 所有需要统计时长的增项服务必须完成（必须条件）
4. 不能存在未开始但需要统计时长的增项服务（必须条件）

**优化点**：
- 明确注释说明此方法主要用于增项服务结束计时触发的订单完成检查
- 强调未付款增项服务和时长统计增项服务都是必须条件

## 业务逻辑说明

### 主动完成订单流程
1. 检查订单状态是否为"服务中"
2. 检查是否存在未付款的附加服务
3. 统一处理所有需要时长统计的服务：
   - 主服务：必然触发结束计时
   - 增项服务：只有已开始的才触发结束计时
4. 更新订单状态为"已完成"
5. 完成所有已支付的追加服务

### 服务结束计时触发订单完成流程
1. 员工结束单个服务项目计时
2. 系统自动检查是否需要结束整个订单：
   - 所有主服务必须完成
   - 所有需要统计时长的增项服务必须完成
   - 不能存在未开始但需要统计时长的增项服务
   - 不能存在未付款的附加服务
3. 如果满足条件，自动调用订单完成逻辑

## 关键改进点

1. **逻辑一致性**：两种场景现在使用相同的未付款增项服务检查逻辑
2. **主服务处理**：明确主服务在订单完成时必然触发结束计时
3. **增项服务处理**：主订单完成时，只有已开始的增项服务才会触发结束计时
4. **自动完成检查**：增项服务结束时，严格检查是否满足触发主订单结束的所有条件

## 影响范围

- **订单完成接口**：`POST /orders/{orderId}/complete`
- **服务结束计时接口**：`POST /employee/service-duration/end`
- **相关服务类**：
  - `OrderService.order_complete()`
  - `ServiceDurationRecordService.endService()`
  - `ServiceDurationRecordService.checkAndCompleteOrderIfAllServicesFinished()`

## 测试建议

建议测试以下场景：
1. 主动完成订单时，存在未开始的增项服务
2. 主动完成订单时，存在未付款的增项服务
3. 服务结束计时触发订单完成时，检查所有条件
4. 主服务和增项服务的结束计时逻辑差异
