import { Rule, RuleType } from '@midwayjs/validate';
import { BaseQueryDTO } from './BaseDTO';

/**
 * 创建投诉建议DTO
 */
export class CreateComplaintDto {
  @Rule(
    RuleType.string()
      .valid('complaint', 'suggestion')
      .required()
      .error(new Error('大类必须是complaint或suggestion'))
  )
  /** 大类：complaint-投诉，suggestion-建议 */
  category: 'complaint' | 'suggestion';

  @Rule(
    RuleType.string()
      .valid('order', 'employee', 'platform', 'service')
      .required()
      .error(new Error('小类必须是order、employee、platform或service'))
  )
  /** 小类：order-订单，employee-人员，platform-平台，service-服务 */
  subCategory: 'order' | 'employee' | 'platform' | 'service';

  @Rule(
    RuleType.string()
      .required()
      .max(200)
      .error(new Error('标题不能为空且不能超过200字符'))
  )
  /** 投诉建议标题 */
  title: string;

  @Rule(
    RuleType.string()
      .required()
      .max(2000)
      .error(new Error('内容不能为空且不能超过2000字符'))
  )
  /** 投诉建议内容 */
  content: string;

  @Rule(RuleType.number().optional().error(new Error('订单ID必须是数字')))
  /** 关联订单ID（订单投诉时必填） */
  orderId?: number;

  @Rule(RuleType.number().optional().error(new Error('员工ID必须是数字')))
  /** 关联员工ID（人员投诉时必填，订单投诉时可选，系统会自动从订单获取） */
  employeeId?: number;

  @Rule(
    RuleType.string()
      .optional()
      .max(100)
      .error(new Error('联系方式不能超过100字符'))
  )
  /** 联系方式 */
  contactInfo?: string;

  @Rule(
    RuleType.array()
      .items(RuleType.string())
      .max(6)
      .optional()
      .error(new Error('图片最多6张'))
  )
  /** 图片URL数组 */
  photoURLs?: string[];
}

/**
 * 更新投诉建议DTO
 */
export class UpdateComplaintDto {
  @Rule(
    RuleType.string()
      .valid('complaint', 'suggestion')
      .optional()
      .error(new Error('大类必须是complaint或suggestion'))
  )
  /** 大类：complaint-投诉，suggestion-建议 */
  category?: 'complaint' | 'suggestion';

  @Rule(
    RuleType.string()
      .valid('order', 'employee', 'platform', 'service')
      .optional()
      .error(new Error('小类必须是order、employee、platform或service'))
  )
  /** 小类：order-订单，employee-人员，platform-平台，service-服务 */
  subCategory?: 'order' | 'employee' | 'platform' | 'service';

  @Rule(
    RuleType.string()
      .optional()
      .max(200)
      .error(new Error('标题不能超过200字符'))
  )
  /** 投诉建议标题 */
  title?: string;

  @Rule(
    RuleType.string()
      .optional()
      .max(2000)
      .error(new Error('内容不能超过2000字符'))
  )
  /** 投诉建议内容 */
  content?: string;

  @Rule(RuleType.number().optional().error(new Error('订单ID必须是数字')))
  /** 关联订单ID */
  orderId?: number;

  @Rule(RuleType.number().optional().error(new Error('员工ID必须是数字')))
  /** 关联员工ID */
  employeeId?: number;

  @Rule(
    RuleType.string()
      .optional()
      .max(100)
      .error(new Error('联系方式不能超过100字符'))
  )
  /** 联系方式 */
  contactInfo?: string;

  @Rule(
    RuleType.array()
      .items(RuleType.string())
      .max(6)
      .optional()
      .error(new Error('图片最多6张'))
  )
  /** 图片URL数组 */
  photoURLs?: string[];
}

/**
 * 查询投诉建议DTO
 */
export class QueryComplaintDto extends BaseQueryDTO {
  @Rule(RuleType.number().optional().error(new Error('客户ID必须是数字')))
  customerId?: number;

  @Rule(RuleType.number().optional().error(new Error('订单ID必须是数字')))
  orderId?: number;

  @Rule(RuleType.number().optional().error(new Error('员工ID必须是数字')))
  employeeId?: number;

  @Rule(
    RuleType.string()
      .valid('complaint', 'suggestion')
      .optional()
      .error(new Error('大类必须是complaint或suggestion'))
  )
  category?: 'complaint' | 'suggestion';

  @Rule(
    RuleType.string()
      .valid('order', 'employee', 'platform', 'service')
      .optional()
      .error(new Error('小类必须是order、employee、platform或service'))
  )
  subCategory?: 'order' | 'employee' | 'platform' | 'service';

  @Rule(
    RuleType.string()
      .valid('pending', 'processing', 'resolved', 'closed')
      .optional()
      .error(new Error('状态必须是pending、processing、resolved或closed'))
  )
  status?: 'pending' | 'processing' | 'resolved' | 'closed';

  @Rule(RuleType.string().optional().error(new Error('关键词必须是字符串')))
  keyword?: string;

  @Rule(RuleType.string().optional().error(new Error('开始日期格式错误')))
  startDate?: string;

  @Rule(RuleType.string().optional().error(new Error('结束日期格式错误')))
  endDate?: string;

  @Rule(RuleType.number().optional().error(new Error('处理人员ID必须是数字')))
  handlerId?: number;
}

/**
 * 处理投诉建议DTO
 */
export class HandleComplaintDto {
  @Rule(
    RuleType.string()
      .valid('processing', 'resolved', 'closed')
      .required()
      .error(new Error('状态必须是processing、resolved或closed'))
  )
  /** 处理状态 */
  status: 'processing' | 'resolved' | 'closed';

  @Rule(
    RuleType.string()
      .required()
      .max(2000)
      .error(new Error('处理结果不能为空且不能超过2000字符'))
  )
  /** 处理结果 */
  result: string;

  @Rule(RuleType.number().required().error(new Error('处理人员ID不能为空')))
  /** 处理人员ID */
  handlerId: number;
}

/**
 * 投诉建议统计DTO
 */
export class ComplaintStatisticsDto {
  @Rule(RuleType.string().optional().error(new Error('开始日期格式错误')))
  startDate?: string;

  @Rule(RuleType.string().optional().error(new Error('结束日期格式错误')))
  endDate?: string;

  @Rule(
    RuleType.string()
      .valid('complaint', 'suggestion')
      .optional()
      .error(new Error('大类必须是complaint或suggestion'))
  )
  category?: 'complaint' | 'suggestion';

  @Rule(
    RuleType.string()
      .valid('order', 'employee', 'platform', 'service')
      .optional()
      .error(new Error('小类必须是order、employee、platform或service'))
  )
  subCategory?: 'order' | 'employee' | 'platform' | 'service';
}

/**
 * 员工创建建议DTO
 */
export class CreateEmployeeSuggestionDto {
  @Rule(
    RuleType.string()
      .valid('platform', 'service', 'workflow')
      .required()
      .error(new Error('小类必须是platform、service或workflow'))
  )
  /** 小类：platform-平台，service-服务 */
  subCategory: 'platform' | 'service';

  @Rule(
    RuleType.string()
      .required()
      .max(200)
      .error(new Error('标题不能为空且不能超过200字符'))
  )
  /** 建议标题 */
  title: string;

  @Rule(
    RuleType.string()
      .required()
      .max(2000)
      .error(new Error('内容不能为空且不能超过2000字符'))
  )
  /** 建议内容 */
  content: string;

  @Rule(
    RuleType.string()
      .optional()
      .min(0)
      .max(100)
      .error(new Error('联系方式不能超过100字符'))
  )
  /** 联系方式 */
  contactInfo?: string;

  @Rule(
    RuleType.array()
      .items(RuleType.string())
      .max(6)
      .optional()
      .error(new Error('图片最多6张'))
  )
  /** 图片URL数组 */
  photoURLs?: string[];
}

/**
 * 员工更新建议DTO
 */
export class UpdateEmployeeSuggestionDto {
  @Rule(
    RuleType.string()
      .valid('platform', 'service', 'workflow')
      .optional()
      .error(new Error('小类必须是platform、service或workflow'))
  )
  /** 小类：platform-平台，service-服务 */
  subCategory?: 'platform' | 'service';

  @Rule(
    RuleType.string()
      .optional()
      .max(200)
      .error(new Error('标题不能超过200字符'))
  )
  /** 建议标题 */
  title?: string;

  @Rule(
    RuleType.string()
      .optional()
      .max(2000)
      .error(new Error('内容不能超过2000字符'))
  )
  /** 建议内容 */
  content?: string;

  @Rule(
    RuleType.string()
      .optional()
      .max(100)
      .error(new Error('联系方式不能超过100字符'))
  )
  /** 联系方式 */
  contactInfo?: string;

  @Rule(
    RuleType.array()
      .items(RuleType.string())
      .max(6)
      .optional()
      .error(new Error('图片最多6张'))
  )
  /** 图片URL数组 */
  photoURLs?: string[];
}

/**
 * 员工查询建议DTO
 */
export class QueryEmployeeSuggestionDto extends BaseQueryDTO {
  @Rule(
    RuleType.string()
      .valid('platform', 'service', 'workflow')
      .optional()
      .error(new Error('小类必须是platform、service或workflow'))
  )
  subCategory?: 'platform' | 'service';

  @Rule(
    RuleType.string()
      .valid('pending', 'processing', 'resolved', 'closed')
      .optional()
      .error(new Error('状态必须是pending、processing、resolved或closed'))
  )
  status?: 'pending' | 'processing' | 'resolved' | 'closed';

  @Rule(RuleType.string().optional().error(new Error('关键词必须是字符串')))
  keyword?: string;

  @Rule(RuleType.string().optional().error(new Error('开始日期格式错误')))
  startDate?: string;

  @Rule(RuleType.string().optional().error(new Error('结束日期格式错误')))
  endDate?: string;
}
