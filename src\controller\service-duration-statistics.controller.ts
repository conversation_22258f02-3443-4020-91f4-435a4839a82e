import { Controller, Get, Query, Inject } from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { ServiceDurationStatisticsService } from '../service/service-duration-statistics.service';

@Controller('/service-duration-statistics')
export class ServiceDurationStatisticsController {
  @Inject()
  ctx: Context;

  @Inject()
  serviceDurationStatisticsService: ServiceDurationStatisticsService;

  /**
   * 获取服务时长统计列表
   */
  @Get('/list')
  async getServiceDurationList(
    @Query()
    query: {
      startDate?: string;
      endDate?: string;
      employeeId?: number;
      serviceId?: number;
      orderId?: number;
      page?: number;
      pageSize?: number;
    }
  ) {
    return await this.serviceDurationStatisticsService.getServiceDurationStatistics(
      query
    );
  }

  /**
   * 获取服务时长概览统计
   */
  @Get('/overview')
  async getServiceDurationOverview(
    @Query()
    query: {
      startDate?: string;
      endDate?: string;
      employeeId?: number;
    }
  ) {
    return await this.serviceDurationStatisticsService.getServiceDurationOverview(
      query
    );
  }

  /**
   * 按服务类型统计服务时长
   */
  @Get('/by-service-type')
  async getServiceDurationByServiceType(
    @Query()
    query: {
      startDate?: string;
      endDate?: string;
      employeeId?: number;
    }
  ) {
    return await this.serviceDurationStatisticsService.getServiceDurationByServiceType(
      query
    );
  }

  /**
   * 按员工统计服务时长
   */
  @Get('/by-employee')
  async getServiceDurationByEmployee(
    @Query()
    query: {
      startDate?: string;
      endDate?: string;
      page?: number;
      pageSize?: number;
    }
  ) {
    return await this.serviceDurationStatisticsService.getServiceDurationByEmployee(
      query
    );
  }
}
